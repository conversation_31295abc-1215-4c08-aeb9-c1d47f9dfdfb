// This file sets up the main entry point for the application, initializing the server and middleware.

const express = require('express');
const app = express();
const settings = require('../config/settings');

// Middleware for parsing JSON requests
app.use(express.json());

// Middleware for serving static files from the public directory
app.use(express.static('public'));

// Example route
app.get('/', (req, res) => {
    res.send('Welcome to the RapidTransport application!');
});

// Error handling middleware
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(500).send('Something broke!');
});

// Start the server
const PORT = settings.PORT || 3000;
app.listen(PORT, () => {
    console.log(`Server is running on http://localhost:${PORT}`);
});